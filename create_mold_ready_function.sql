-- Function to enqueue a check_if_mold_is_ready task
CREATE OR REPLACE FUNCTION fn_enqueue_check_if_mold_is_ready()
RETURNS TRIGGER AS $$
DECLARE
  mold_id INTEGER;
  mold_record RECORD;
BEGIN
  -- Determine which table triggered this function
  IF TG_TABLE_NAME = 't_molds' THEN
    -- If triggered from t_molds, use NEW.id directly
    mold_id := NEW.id;

    -- Enqueue the task
    INSERT INTO t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at
    ) VALUES (
      'check_if_mold_is_ready',
      jsonb_build_object('id', mold_id),
      'pending',
      NOW()
    );

  ELSIF TG_TABLE_NAME = 't_images' THEN
    -- If triggered from t_images, use NEW.record_id
    mold_id := NEW.record_id;

    -- Enqueue the task
    INSERT INTO t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at
    ) VALUES (
      'check_if_mold_is_ready',
      jsonb_build_object('id', mold_id),
      'pending',
      NOW()
    );

  ELSIF TG_TABLE_NAME = 't_brands' THEN
    -- If triggered from t_brands, find all unpublished molds for this brand
    -- and enqueue a task for each one
    IF NEW.shopify_collection_created_at IS NOT NULL AND
       (OLD.shopify_collection_created_at IS NULL OR TG_OP = 'INSERT') THEN

      FOR mold_record IN
        SELECT id FROM t_molds
        WHERE brand_id = NEW.id
        AND shopify_collection_created_at IS NULL
      LOOP
        -- Enqueue a task for each unpublished mold
        INSERT INTO t_task_queue (
          task_type,
          payload,
          status,
          scheduled_at
        ) VALUES (
          'check_if_mold_is_ready',
          jsonb_build_object('id', mold_record.id),
          'pending',
          NOW()
        );
      END LOOP;
    END IF;
  ELSE
    RAISE EXCEPTION 'Unexpected table name: %', TG_TABLE_NAME;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop old trigger first
DROP TRIGGER IF EXISTS tr_try_publish_collection_mold ON public.t_molds;

-- 1) Trigger on t_molds
DROP TRIGGER IF EXISTS trg_enqueue_check_on_molds ON public.t_molds;
CREATE TRIGGER trg_enqueue_check_on_molds
  AFTER INSERT OR UPDATE OF
        mold,
        brand_id,
        code,
        type,
        speed,
        glide,
        turn,
        fade,
        description,
        embargo_until,
        shopify_collection_created_at
  ON public.t_molds
  FOR EACH ROW
  WHEN (NEW.shopify_collection_created_at IS NULL)
  EXECUTE FUNCTION fn_enqueue_check_if_mold_is_ready();

-- 2) Trigger on t_images
DROP TRIGGER IF EXISTS trg_enqueue_check_on_images ON public.t_images;
CREATE TRIGGER trg_enqueue_check_on_images
  AFTER INSERT OR UPDATE OF image_verified
  ON public.t_images
  FOR EACH ROW
  WHEN (
      NEW.table_name = 't_molds'
      AND NEW.image_verified IS TRUE
  )
  EXECUTE FUNCTION fn_enqueue_check_if_mold_is_ready();

-- 3) Trigger on t_brands
DROP TRIGGER IF EXISTS trg_enqueue_check_on_brands ON public.t_brands;
CREATE TRIGGER trg_enqueue_check_on_brands
  AFTER INSERT OR UPDATE OF shopify_collection_created_at
  ON public.t_brands
  FOR EACH ROW
  WHEN (NEW.shopify_collection_created_at IS NOT NULL)
  EXECUTE FUNCTION fn_enqueue_check_if_mold_is_ready();
